import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsOptional } from "class-validator";

export class CreateBloodSugarDto {
    @ApiProperty({
        description: 'User ID associated with this blood sugar measurement',
        example: '507f1f77bcf86cd799439012',
    })
   @IsNotEmpty()
    userId: string;

    @ApiProperty({
        description: 'Glycemic level measured',
        example: 100,
    })
   @IsNotEmpty()
   glycemic: number;

    @ApiProperty({
        description: 'Ketone level measured',
        example: 0.5,
    })
   @IsNotEmpty()
    ketone: number;

    @ApiProperty({
        description: 'Type of measurement (pre-meal, post-meal, fasting, bedTime)',
        example: 'pre-meal',
        enum: ['pre-meal','post-meal','fasting','bedTime'],
        required: true,
    })
   @IsNotEmpty()
    measurementType: string;

    @ApiProperty({
        description: 'Source of the measurement (Manual, Device, Camera)',
        example: 'Manual',
        enum: ['Manual', 'Device', 'Camera'],
        required: true,
    })
   @IsNotEmpty()
    source: string;

    @ApiProperty({
        description: 'Name of the device used for measurement',
        example: 'OneTouch Ultra 2',
        required: false,
    })
   @IsOptional()
    deviceName?: string;

    @ApiProperty({
        description: 'Comment or note related to the measurement',
        example: 'Meal was high in carbs',
        required: false,
    })
   @IsOptional()
    comment?: string;

    @ApiProperty({
        description: 'Date and time of the measurement',
        example: '2024-01-15T10:30:00.000Z',
    })
   @IsOptional()
    measurementDate?: string;

    @ApiProperty({
        description: 'Activities performed before the measurement',
        example: ['Exercise', 'Meal'],
        required: true,
    })
   @IsNotEmpty()
    preMeasurementActivities: string[];

    @ApiProperty({
        description: 'Custom note for pre-measurement activities',
        example: 'Had a high-carb meal',
        required: false,
    })
   @IsOptional()
    customActivityNote?: string;
}
