import { Injectable, InternalServerErrorException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { BloodSugar } from "./schema/bloodSugar.schema";
import { Model } from "mongoose";
import { IBloodSugar } from "./interfaces/bloodSugar.interface";
import { CreateBloodSugarDto } from "./dto/createBloodSugar.dto";

@Injectable()
export class BloodSugarService {
    constructor(@InjectModel(BloodSugar.name) private bloodSugarModel: Model<IBloodSugar>) {}
    async create(bloodSugarDto: CreateBloodSugarDto): Promise<IBloodSugar> {
        try {
            const record= (await this.bloodSugarModel.create(bloodSugarDto)).populate({path:'userId',select:'-password'});
            return record;
        } catch (error) {
            throw new InternalServerErrorException(`Failed to create blood sugar measurement: ${error.message}`);
        }
    }

    async findAll(): Promise<IBloodSugar[]> {
        try {
            return await this.bloodSugarModel.find().populate({path:'userId',select:'-password'}).exec();
        } catch (error) {
            throw new InternalServerErrorException(`Failed to fetch blood sugar measurements: ${error.message}`);
        }
    }

    async findOne(id: string): Promise<IBloodSugar> {
        try {
            const bloodSugar = await this.bloodSugarModel.findById(id).populate({path:'userId',select:'-password'}).exec();
            if (!bloodSugar) {
                throw new Error('Blood sugar measurement not found');
            }
            return bloodSugar;
        }
        catch (error) {
            throw new InternalServerErrorException(`Failed to fetch blood sugar measurement: ${error.message}`);
        }
    }

    async update(id: string, bloodSugarDto: CreateBloodSugarDto): Promise<IBloodSugar> {
        try {
            const bloodSugar = await this.bloodSugarModel.findByIdAndUpdate(id, bloodSugarDto, { new: true }).populate({path:'userId',select:'-password'}).exec();
            if (!bloodSugar) {
                throw new Error('Blood sugar measurement not found');
            }
            return bloodSugar;
        }
        catch (error) {
            throw new InternalServerErrorException(`Failed to update blood sugar measurement: ${error.message}`);
        }
    }

    async delete(id: string): Promise<string> {
        try {
            const bloodSugar = await this.bloodSugarModel.findByIdAndDelete(id).exec();
            if (!bloodSugar) {
                throw new Error('Blood sugar measurement not found');
            }
            return 'Blood sugar measurement deleted';
        }
        catch (error) {
            throw new InternalServerErrorException(`Failed to delete blood sugar measurement: ${error.message}`);
        }
    }

}